<?php
if (!defined('ABSPATH')) exit;

class AI_Auto_Blog_Diagnostics {
    
    public static function run_server_diagnostics() {
        $results = [];
        
        // Test 1: Basic connectivity
        $results['connectivity'] = self::test_connectivity();
        
        // Test 2: Timeout limits
        $results['timeouts'] = self::test_timeout_limits();
        
        // Test 3: Server configuration
        $results['server_config'] = self::get_server_config();
        
        // Test 4: Flowise endpoint test
        $results['flowise_test'] = self::test_flowise_endpoint();
        
        return $results;
    }
    
    private static function test_connectivity() {
        $agent_url = get_option('ai_auto_blog_agent_url', '');
        if (empty($agent_url)) {
            return ['status' => 'error', 'message' => 'No agent URL configured'];
        }
        
        if (strpos($agent_url, 'https://') !== 0) {
            $agent_url = "https://llminabox.criticalfutureglobal.com/api/v1/prediction/{$agent_url}";
        }
        
        $start_time = microtime(true);
        $response = wp_remote_get($agent_url, [
            'timeout' => 10,
            'sslverify' => true
        ]);
        $end_time = microtime(true);
        
        if (is_wp_error($response)) {
            return [
                'status' => 'error',
                'message' => $response->get_error_message(),
                'response_time' => round(($end_time - $start_time) * 1000, 2) . 'ms'
            ];
        }
        
        return [
            'status' => 'success',
            'http_code' => wp_remote_retrieve_response_code($response),
            'response_time' => round(($end_time - $start_time) * 1000, 2) . 'ms'
        ];
    }
    
    private static function test_timeout_limits() {
        $limits = [];
        
        // PHP limits
        $limits['php_max_execution_time'] = ini_get('max_execution_time') . ' seconds';
        $limits['php_memory_limit'] = ini_get('memory_limit');
        
        // WordPress limits
        $limits['wp_http_timeout'] = defined('WP_HTTP_TIMEOUT') ? WP_HTTP_TIMEOUT . ' seconds' : 'Default (5 seconds)';
        
        // Plugin setting
        $limits['plugin_ai_timeout'] = get_option('ai_auto_blog_api_timeout', 300) . ' seconds';
        
        return $limits;
    }
    
    private static function get_server_config() {
        $config = [];
        
        // Server software
        $config['server_software'] = $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown';
        
        // PHP version
        $config['php_version'] = PHP_VERSION;
        
        // WordPress version
        $config['wordpress_version'] = get_bloginfo('version');
        
        // cURL version
        if (function_exists('curl_version')) {
            $curl_info = curl_version();
            $config['curl_version'] = $curl_info['version'];
        } else {
            $config['curl_version'] = 'Not available';
        }
        
        return $config;
    }
    
    private static function test_flowise_endpoint() {
        $agent_url = get_option('ai_auto_blog_agent_url', '');
        if (empty($agent_url)) {
            return ['status' => 'error', 'message' => 'No agent URL configured'];
        }
        
        if (strpos($agent_url, 'https://') !== 0) {
            $agent_url = "https://llminabox.criticalfutureglobal.com/api/v1/prediction/{$agent_url}";
        }
        
        // Test with a simple payload
        $payload = ['question' => 'Test connection'];
        
        $start_time = microtime(true);
        $response = wp_remote_post($agent_url, [
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json'
            ],
            'body' => json_encode($payload),
            'timeout' => 30, // Short timeout for diagnostic
            'sslverify' => true
        ]);
        $end_time = microtime(true);
        
        if (is_wp_error($response)) {
            return [
                'status' => 'error',
                'message' => $response->get_error_message(),
                'response_time' => round(($end_time - $start_time) * 1000, 2) . 'ms'
            ];
        }
        
        $http_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        
        return [
            'status' => $http_code === 200 ? 'success' : 'warning',
            'http_code' => $http_code,
            'response_time' => round(($end_time - $start_time) * 1000, 2) . 'ms',
            'response_size' => strlen($body) . ' bytes',
            'response_preview' => substr($body, 0, 200) . (strlen($body) > 200 ? '...' : '')
        ];
    }
}

// Add diagnostics page to admin menu
add_action('admin_menu', function() {
    add_submenu_page(
        'ai-auto-blog',
        __('Diagnostics', 'ai-auto-blog'),
        __('Diagnostics', 'ai-auto-blog'),
        'manage_options',
        'ai-auto-blog-diagnostics',
        'ai_auto_blog_diagnostics_page'
    );
});

function ai_auto_blog_diagnostics_page() {
    if (isset($_POST['run_diagnostics'])) {
        $diagnostics = AI_Auto_Blog_Diagnostics::run_server_diagnostics();
    }
    
    ?>
    <div class="wrap">
        <h1><?php _e('Server Diagnostics', 'ai-auto-blog'); ?></h1>
        
        <div class="notice notice-info">
            <p><?php _e('This tool helps diagnose server configuration issues that may cause 504 Gateway Timeout errors.', 'ai-auto-blog'); ?></p>
        </div>
        
        <form method="post">
            <p>
                <button type="submit" name="run_diagnostics" class="button button-primary">
                    <?php _e('Run Diagnostics', 'ai-auto-blog'); ?>
                </button>
            </p>
        </form>
        
        <?php if (isset($diagnostics)): ?>
            <div class="diagnostics-results">
                <h2><?php _e('Diagnostic Results', 'ai-auto-blog'); ?></h2>
                
                <div class="card">
                    <h3><?php _e('Connectivity Test', 'ai-auto-blog'); ?></h3>
                    <table class="widefat">
                        <?php foreach ($diagnostics['connectivity'] as $key => $value): ?>
                            <tr>
                                <td><strong><?php echo esc_html(ucfirst(str_replace('_', ' ', $key))); ?>:</strong></td>
                                <td><?php echo esc_html($value); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </table>
                </div>
                
                <div class="card">
                    <h3><?php _e('Timeout Configuration', 'ai-auto-blog'); ?></h3>
                    <table class="widefat">
                        <?php foreach ($diagnostics['timeouts'] as $key => $value): ?>
                            <tr>
                                <td><strong><?php echo esc_html(ucfirst(str_replace('_', ' ', $key))); ?>:</strong></td>
                                <td><?php echo esc_html($value); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </table>
                </div>
                
                <div class="card">
                    <h3><?php _e('Server Configuration', 'ai-auto-blog'); ?></h3>
                    <table class="widefat">
                        <?php foreach ($diagnostics['server_config'] as $key => $value): ?>
                            <tr>
                                <td><strong><?php echo esc_html(ucfirst(str_replace('_', ' ', $key))); ?>:</strong></td>
                                <td><?php echo esc_html($value); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </table>
                </div>
                
                <div class="card">
                    <h3><?php _e('Flowise Endpoint Test', 'ai-auto-blog'); ?></h3>
                    <table class="widefat">
                        <?php foreach ($diagnostics['flowise_test'] as $key => $value): ?>
                            <tr>
                                <td><strong><?php echo esc_html(ucfirst(str_replace('_', ' ', $key))); ?>:</strong></td>
                                <td><?php echo esc_html($value); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </table>
                </div>
                
                <?php if ($diagnostics['connectivity']['status'] === 'error' || 
                         (isset($diagnostics['flowise_test']['http_code']) && $diagnostics['flowise_test']['http_code'] === 504)): ?>
                    <div class="notice notice-error">
                        <h3><?php _e('Recommended Actions', 'ai-auto-blog'); ?></h3>
                        <ul>
                            <li><?php _e('Contact your hosting provider to increase nginx proxy timeouts', 'ai-auto-blog'); ?></li>
                            <li><?php _e('Request these nginx settings: proxy_read_timeout 600s; proxy_connect_timeout 600s; proxy_send_timeout 600s;', 'ai-auto-blog'); ?></li>
                            <li><?php _e('Consider using a different server or hosting provider that supports longer request timeouts', 'ai-auto-blog'); ?></li>
                        </ul>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
    
    <style>
    .diagnostics-results .card {
        margin: 20px 0;
        padding: 15px;
        background: #fff;
        border: 1px solid #ccd0d4;
        border-radius: 4px;
    }
    
    .diagnostics-results .card h3 {
        margin-top: 0;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
    }
    
    .diagnostics-results table {
        margin-top: 10px;
    }
    
    .diagnostics-results table td {
        padding: 8px 12px;
        border-bottom: 1px solid #f0f0f1;
    }
    
    .diagnostics-results table td:first-child {
        width: 200px;
        background: #f9f9f9;
    }
    </style>
    <?php
}
