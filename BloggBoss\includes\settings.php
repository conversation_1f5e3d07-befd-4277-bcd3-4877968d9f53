<?php
if (!defined('ABSPATH')) exit;

function ai_auto_blog_add_menu() {
    $menu_icon = 'dashicons-edit';
    
    add_menu_page(
        __('AI Auto Blog Settings', 'ai-auto-blog'),
        __('AI Auto Blog', 'ai-auto-blog'),
        'manage_options',
        'ai-auto-blog',
        'ai_auto_blog_settings_page',
        $menu_icon,
        25
    );

    add_submenu_page(
        'ai-auto-blog',
        __('Fetch AI Blog', 'ai-auto-blog'),
        __('Fetch Blog', 'ai-auto-blog'),
        'manage_options',
        'ai-auto-blog-fetch',
        'ai_auto_blog_fetch_and_post_button'
    );

    add_submenu_page(
        'ai-auto-blog',
        __('Logs', 'ai-auto-blog'),
        __('Logs', 'ai-auto-blog'),
        'manage_options',
        'ai-auto-blog-logs',
        'ai_auto_blog_display_logs'
    );
}
add_action('admin_menu', 'ai_auto_blog_add_menu');

function ai_auto_blog_settings_page() {
    // Force refresh schedule status if settings were just saved
    if (isset($_GET['settings-updated']) && $_GET['settings-updated'] == 'true') {
        require_once plugin_dir_path(__FILE__) . 'scheduler.php';
        AI_Auto_Blog_Scheduler::force_reschedule();
    }
    
    ?>
    <div class="wrap">
        <h1><?php _e('AI Auto Blog Settings', 'ai-auto-blog'); ?></h1>
        <form method="post" action="options.php">
            <?php
            settings_fields('ai_auto_blog_options');
            do_settings_sections('ai-auto-blog');
            submit_button();
            ?>
        </form>
        
        <div class="card">
            <h2><?php _e('Automation Status', 'ai-auto-blog'); ?></h2>
            <?php
            $next_run = wp_next_scheduled('ai_auto_blog_daily_post');
            $is_enabled = get_option('ai_auto_blog_automation_enabled', 1);
            ?>
            
            <p>
                <?php _e('Status:', 'ai-auto-blog'); ?> 
                <strong><?php echo $is_enabled ? __('Enabled', 'ai-auto-blog') : __('Disabled', 'ai-auto-blog'); ?></strong>
            </p>
            
            <p>
                <?php _e('Next scheduled post:', 'ai-auto-blog'); ?> 
                <strong>
                    <?php
                    echo $next_run && $is_enabled ? date_i18n(get_option('date_format') . ' ' . get_option('time_format'), $next_run) : __('Not scheduled', 'ai-auto-blog');
                    ?>
                </strong>
            </p>
            
            <?php if ($is_enabled && $next_run): ?>
            <p>
                <?php _e('Current time:', 'ai-auto-blog'); ?> 
                <strong><?php echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), current_time('timestamp')); ?></strong>
            </p>
            <?php endif; ?>
            
            <?php if (isset($_GET['settings-updated']) && $_GET['settings-updated'] == 'true'): ?>
            <div class="notice notice-success inline">
                <p><?php _e('Settings saved and schedule updated!', 'ai-auto-blog'); ?></p>
            </div>
            <?php endif; ?>
            
            <p>
                <a href="<?php echo esc_url(add_query_arg(['page' => 'ai-auto-blog', 'action' => 'refresh-schedule', '_wpnonce' => wp_create_nonce('refresh_schedule')])); ?>" class="button button-secondary">
                    <?php _e('Refresh Schedule', 'ai-auto-blog'); ?>
                </a>
            </p>
        </div>
    </div>
    <?php
}

// Handle manual schedule refresh
add_action('admin_init', function() {
    if (isset($_GET['action']) && $_GET['action'] === 'refresh-schedule' && isset($_GET['page']) && $_GET['page'] === 'ai-auto-blog') {
        if (!wp_verify_nonce($_GET['_wpnonce'], 'refresh_schedule')) {
            wp_die(__('Security check failed', 'ai-auto-blog'));
        }
        
        require_once plugin_dir_path(__FILE__) . 'scheduler.php';
        AI_Auto_Blog_Scheduler::force_reschedule();
        
        wp_redirect(admin_url('admin.php?page=ai-auto-blog&schedule-refreshed=true'));
        exit;
    }
});

function ai_auto_blog_register_settings() {
    register_setting('ai_auto_blog_options', 'ai_auto_blog_site_url');
    register_setting('ai_auto_blog_options', 'ai_auto_blog_username');
    register_setting('ai_auto_blog_options', 'ai_auto_blog_password');
    register_setting('ai_auto_blog_options', 'ai_auto_blog_category');
    register_setting('ai_auto_blog_options', 'ai_auto_blog_agent_url');
    register_setting('ai_auto_blog_options', 'ai_auto_blog_prompt');
    register_setting('ai_auto_blog_options', 'ai_auto_blog_schedule_time');
    register_setting('ai_auto_blog_options', 'ai_auto_blog_schedule_frequency');
    register_setting('ai_auto_blog_options', 'ai_auto_blog_automation_enabled');
    register_setting('ai_auto_blog_options', 'ai_auto_blog_api_timeout');

    add_settings_section(
        'ai_auto_blog_main',
        __('API Configuration', 'ai-auto-blog'),
        null,
        'ai-auto-blog'
    );

    add_settings_section(
        'ai_auto_blog_scheduler',
        __('Posting Schedule', 'ai-auto-blog'),
        null,
        'ai-auto-blog'
    );

    ai_auto_blog_add_settings_fields();
}
add_action('admin_init', 'ai_auto_blog_register_settings');

function ai_auto_blog_add_settings_fields() {
    add_settings_field(
        'ai_auto_blog_site_url',
        __('WordPress Site URL', 'ai-auto-blog'),
        'ai_auto_blog_site_url_callback',
        'ai-auto-blog',
        'ai_auto_blog_main'
    );

    add_settings_field(
        'ai_auto_blog_username',
        __('Admin Username', 'ai-auto-blog'),
        'ai_auto_blog_username_callback',
        'ai-auto-blog',
        'ai_auto_blog_main'
    );

    add_settings_field(
        'ai_auto_blog_password',
        __('Application Password', 'ai-auto-blog'),
        'ai_auto_blog_password_callback',
        'ai-auto-blog',
        'ai_auto_blog_main'
    );

    add_settings_field(
        'ai_auto_blog_category',
        __('Post Category', 'ai-auto-blog'),
        'ai_auto_blog_category_callback',
        'ai-auto-blog',
        'ai_auto_blog_main'
    );

    add_settings_field(
        'ai_auto_blog_agent_url',
        __('AI Agent URL', 'ai-auto-blog'),
        'ai_auto_blog_agent_url_callback',
        'ai-auto-blog',
        'ai_auto_blog_main'
    );

    add_settings_field(
        'ai_auto_blog_prompt',
        __('AI Prompt', 'ai-auto-blog'),
        'ai_auto_blog_prompt_callback',
        'ai-auto-blog',
        'ai_auto_blog_main'
    );

    add_settings_field(
        'ai_auto_blog_api_timeout',
        __('API Timeout (seconds)', 'ai-auto-blog'),
        'ai_auto_blog_api_timeout_callback',
        'ai-auto-blog',
        'ai_auto_blog_main'
    );

    add_settings_field(
        'ai_auto_blog_schedule_time',
        __('Posting Time', 'ai-auto-blog'),
        'ai_auto_blog_schedule_time_callback',
        'ai-auto-blog',
        'ai_auto_blog_scheduler'
    );

    add_settings_field(
        'ai_auto_blog_schedule_frequency',
        __('Frequency', 'ai-auto-blog'),
        'ai_auto_blog_schedule_frequency_callback',
        'ai-auto-blog',
        'ai_auto_blog_scheduler'
    );

    add_settings_field(
        'ai_auto_blog_automation_enabled',
        __('Automation', 'ai-auto-blog'),
        'ai_auto_blog_automation_callback',
        'ai-auto-blog',
        'ai_auto_blog_scheduler'
    );
}

function ai_auto_blog_site_url_callback() {
    $value = esc_url(get_option('ai_auto_blog_site_url', ''));
    echo '<input type="url" name="ai_auto_blog_site_url" value="' . $value . '" 
          class="regular-text" placeholder="https://yourwebsite.com" required>';
}

function ai_auto_blog_username_callback() {
    $value = esc_attr(get_option('ai_auto_blog_username', ''));
    echo '<input type="text" name="ai_auto_blog_username" value="' . $value . '" 
          class="regular-text" placeholder="admin" required>';
}

function ai_auto_blog_password_callback() {
    $value = esc_attr(get_option('ai_auto_blog_password', ''));
    echo '<input type="password" name="ai_auto_blog_password" value="' . $value . '" 
          class="regular-text" placeholder="••••••••" required>';
}

function ai_auto_blog_category_callback() {
    $categories = get_categories(['hide_empty' => false]);
    $current = get_option('ai_auto_blog_category', '');
    
    echo '<select name="ai_auto_blog_category" class="regular-text">';
    echo '<option value="">' . esc_html__('Select a category', 'ai-auto-blog') . '</option>';
    foreach ($categories as $category) {
        printf(
            '<option value="%s" %s>%s (ID: %s)</option>',
            esc_attr($category->term_id),
            selected($current, $category->term_id, false),
            esc_html($category->name),
            esc_html($category->term_id)
        );
    }
    echo '</select>';
}

function ai_auto_blog_agent_url_callback() {
    $value = esc_attr(get_option('ai_auto_blog_agent_url', ''));
    echo '<input type="text" name="ai_auto_blog_agent_url" value="' . $value . '" 
          class="regular-text" placeholder="chatflow-id" required>';
}

function ai_auto_blog_prompt_callback() {
    $value = esc_textarea(get_option('ai_auto_blog_prompt', ''));
    echo '<textarea name="ai_auto_blog_prompt" rows="5" class="large-text"
          placeholder="Write your prompt here..." required>' . $value . '</textarea>';
}

function ai_auto_blog_api_timeout_callback() {
    $value = intval(get_option('ai_auto_blog_api_timeout', 300));
    echo '<input type="number" name="ai_auto_blog_api_timeout" value="' . $value . '"
          class="small-text" min="60" max="600" step="30">';
    echo '<p class="description">' . __('Timeout for AI API requests in seconds (60-600). Default: 300 (5 minutes)', 'ai-auto-blog') . '</p>';
}

function ai_auto_blog_schedule_time_callback() {
    $value = esc_attr(get_option('ai_auto_blog_schedule_time', '09:00'));
    echo '<input type="time" name="ai_auto_blog_schedule_time" value="' . $value . '" 
          class="time-picker" required>';
    echo '<p class="description">' . __('Enter time in 24-hour format (HH:MM)', 'ai-auto-blog') . '</p>';
}

function ai_auto_blog_schedule_frequency_callback() {
    $value = get_option('ai_auto_blog_schedule_frequency', 'daily');
    echo '<select name="ai_auto_blog_schedule_frequency" class="regular-text">
            <option value="daily" ' . selected($value, 'daily', false) . '>' . __('Daily', 'ai-auto-blog') . '</option>
            <option value="weekly" ' . selected($value, 'weekly', false) . '>' . __('Weekly', 'ai-auto-blog') . '</option>
          </select>';
}

function ai_auto_blog_automation_callback() {
    $enabled = get_option('ai_auto_blog_automation_enabled', 1);
    echo '<label><input type="checkbox" name="ai_auto_blog_automation_enabled" value="1" ' 
       . checked(1, $enabled, false) . '> ' . __('Enable automatic posting', 'ai-auto-blog') . '</label>';
}

function ai_auto_blog_fetch_and_post_button() {
    $response = '';
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['ai_auto_blog_fetch_post'])) {
        if (!wp_verify_nonce($_POST['_wpnonce'], 'ai_auto_blog_fetch')) {
            $response = '<div class="notice notice-error"><p>' . __('Security verification failed!', 'ai-auto-blog') . '</p></div>';
        } else {
            require_once plugin_dir_path(__FILE__) . 'post-handler.php';
            $result = ai_auto_blog_fetch_and_post();
            $response = '<div class="notice notice-' . ($result['status'] === 'success' ? 'success' : 'error') . '"><pre>' 
                      . esc_html(print_r($result, true)) . '</pre></div>';
        }
    }
    
    ?>
    <div class="wrap">
        <h1><?php _e('Manual Post Generation', 'ai-auto-blog'); ?></h1>
        
        <?php echo $response; ?>

        <form method="post">
            <?php wp_nonce_field('ai_auto_blog_fetch', '_wpnonce'); ?>
            <p class="submit">
                <button type="submit" name="ai_auto_blog_fetch_post" class="button button-primary">
                    <?php _e('Generate Post Now', 'ai-auto-blog'); ?>
                </button>
            </p>
        </form>
    </div>
    <?php
}

function ai_auto_blog_display_logs() {
    $log_file = plugin_dir_path(__FILE__) . '../logs.txt';
    $log_content = __('No logs found', 'ai-auto-blog');
    
    if (file_exists($log_file)) {
        if (is_readable($log_file)) {
            $log_content = esc_textarea(file_get_contents($log_file));
        } else {
            $log_content = __('Log file is not readable', 'ai-auto-blog');
        }
    }
    
    ?>
    <div class="wrap">
        <h1><?php _e('System Logs', 'ai-auto-blog'); ?></h1>
        <div class="log-controls">
            <a href="<?php echo wp_nonce_url(admin_url('admin.php?page=ai-auto-blog-logs&action=clear-logs'), 'clear_logs'); ?>" 
               class="button">
                <?php _e('Clear Logs', 'ai-auto-blog'); ?>
            </a>
        </div>
        <textarea class="log-viewer" rows="20" readonly><?php echo $log_content; ?></textarea>
    </div>
    <?php
}

add_action('admin_init', function() {
    if (isset($_GET['action']) && $_GET['action'] === 'clear-logs' && isset($_GET['page']) && $_GET['page'] === 'ai-auto-blog-logs') {
        if (!wp_verify_nonce($_GET['_wpnonce'], 'clear_logs')) {
            wp_die(__('Security check failed', 'ai-auto-blog'));
        }
        
        $log_file = plugin_dir_path(__FILE__) . '../logs.txt';
        if (file_exists($log_file)) {
            file_put_contents($log_file, '');
        }
        
        wp_redirect(admin_url('admin.php?page=ai-auto-blog-logs'));
        exit;
    }
});