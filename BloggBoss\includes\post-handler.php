<?php
if (!defined('ABSPATH')) exit;

require_once plugin_dir_path(__FILE__) . 'logs.php';
require_once ABSPATH . 'wp-admin/includes/media.php';
require_once ABSPATH . 'wp-admin/includes/file.php';
require_once ABSPATH . 'wp-admin/includes/image.php';

class AI_Auto_Blog_Poster {
    private static $retry_attempts = 3;
    private static $retry_delay = 5;
    private static $ai_api_timeout = 300; // 5 minutes default timeout for AI API

    public static function fetch_and_post() {
        $lock_key = 'ai_auto_blog_lock';
    
    // تحقق مما إذا كانت الوظيفة تعمل بالفعل
    if (get_transient($lock_key)) {
        ai_auto_blog_log('WARNING: Process already running, skipping execution.');
        return [
            'status' => 'error',
            'message' => __('Process already running, skipping execution.', 'ai-auto-blog')
        ];
    }

    // تعيين القفل لمدة قصيرة (مثلاً 5 دقائق)
    set_transient($lock_key, true, 300);
        try {
            ai_auto_blog_log('INFO: Process started - ' . current_time('mysql'));

            $settings = self::validate_settings();
            $ai_content = self::fetch_ai_content($settings);
            $post_id = self::create_wordpress_post($settings, $ai_content);

            if (!empty($ai_content['image_url'])) {
                self::attach_featured_image($post_id, $ai_content['image_url']);
            }

            ai_auto_blog_log("SUCCESS: Post published - ID {$post_id}");
            return [
                'status' => 'success',
                'post_id' => $post_id,
                'message' => __('Post published successfully', 'ai-auto-blog')
            ];

        } catch (Exception $e) {
            ai_auto_blog_log("ERROR: " . $e->getMessage());
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
        finally {
            // إزالة القفل بعد انتهاء العملية
            delete_transient($lock_key);
        }
    }

    private static function validate_settings() {
        $required = [
            'site_url' => get_option('ai_auto_blog_site_url'),
            'username' => get_option('ai_auto_blog_username'),
            'password' => get_option('ai_auto_blog_password'),
            'category' => get_option('ai_auto_blog_category'),
            'agent_url' => get_option('ai_auto_blog_agent_url'),
            'prompt' => get_option('ai_auto_blog_prompt')
        ];

        $missing = array_filter($required, function($v) { return empty($v); });
        if (!empty($missing)) {
            throw new Exception(__('Missing required settings: ', 'ai-auto-blog') . implode(', ', array_keys($missing)));
        }

        // Ensure category is a valid integer
        $required['category'] = intval($required['category']);
        if ($required['category'] <= 0 || !term_exists($required['category'], 'category')) {
            throw new Exception(__('Invalid category ID', 'ai-auto-blog'));
        }

        return $required;
    }

    private static function fetch_ai_content($settings) {
        $fast_mode = get_option('ai_auto_blog_fast_mode', 1);

        if ($fast_mode) {
            // Fast mode: shorter prompt and timeout for nginx compatibility
            $prompt = "Write a brief 300-word blog post about: " . substr($settings['prompt'], 0, 100);
            $timeout = 50; // Stay well under nginx 60s limit
            ai_auto_blog_log("INFO: Fast mode enabled - using 50s timeout and shorter prompt");
        } else {
            // Full mode: original prompt and timeout
            $prompt = $settings['prompt'];
            $timeout = get_option('ai_auto_blog_api_timeout', self::$ai_api_timeout);
            ai_auto_blog_log("INFO: Full mode enabled - using {$timeout}s timeout and full prompt");
        }

        $payload = [
            'question' => "Prompt: {$prompt}\n"
                        . "Site URL: {$settings['site_url']}\n"
                        . "Username: {$settings['username']}\n"
                        . "Password: {$settings['password']}"
                        . ($fast_mode ? "\nIMPORTANT: Keep response under 300 words for faster processing." : "")
        ];

        $agent_url = $settings['agent_url'];
        if (strpos($agent_url, 'https://') !== 0) {
            $agent_url = "https://llminabox.criticalfutureglobal.com/api/v1/prediction/{$agent_url}";
        }

        ai_auto_blog_log("INFO: Using AI API timeout: {$timeout} seconds");

        for ($attempt = 1; $attempt <= self::$retry_attempts; $attempt++) {
            $response = wp_remote_post($agent_url, [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json'
                ],
                'body' => json_encode($payload),
                'timeout' => $timeout, // Configurable timeout for AI processing
                'sslverify' => true
            ]);

            if (is_wp_error($response)) {
                $error_message = $response->get_error_message();
                ai_auto_blog_log("ERROR: WP_Error in API request - " . $error_message);

                // Check if it's a timeout error
                if (strpos($error_message, 'timeout') !== false || strpos($error_message, 'timed out') !== false) {
                    ai_auto_blog_log("WARNING: Request timed out after {$timeout} seconds. Consider increasing the timeout setting or nginx proxy timeout.");
                }

                if ($attempt < self::$retry_attempts) {
                    sleep(self::$retry_delay);
                    ai_auto_blog_log("WARNING: AI API attempt {$attempt} failed, retrying...");
                    continue;
                }
            } else {
                $http_code = wp_remote_retrieve_response_code($response);
                $body = wp_remote_retrieve_body($response);

                ai_auto_blog_log("INFO: API Response - HTTP Code: {$http_code}, Body: " . substr($body, 0, 500) . "...");

                if ($http_code === 200) {
                    $decoded_body = json_decode($body, true);
                    if (isset($decoded_body['text'])) {
                        return [
                            'content' => $decoded_body['text'],
                            'image_url' => $decoded_body['image_url'] ?? ''
                        ];
                    } else {
                        ai_auto_blog_log("ERROR: API response doesn't contain 'text' field");
                    }
                } else if ($http_code === 504) {
                    ai_auto_blog_log("ERROR: 504 Gateway Timeout - This is a server infrastructure issue. The nginx server timed out waiting for Flowise response.");
                    ai_auto_blog_log("SOLUTION: Increase nginx proxy_read_timeout, proxy_connect_timeout, and proxy_send_timeout to 600s or more.");
                } else {
                    ai_auto_blog_log("ERROR: API returned non-200 status code: {$http_code}");
                }
            }

            if ($attempt < self::$retry_attempts) {
                sleep(self::$retry_delay);
                ai_auto_blog_log("WARNING: AI API attempt {$attempt} failed, retrying...");
            }
        }

        throw new Exception(__('Failed to fetch AI content after multiple attempts', 'ai-auto-blog'));
    }

    private static function create_wordpress_post($settings, $content) {
        preg_match('/<h1[^>]*>(.*?)<\/h1>/i', $content['content'], $matches);
        $title = $matches[1] ?? __('$title', 'ai-auto-blog');
        
        $cleaned_content = wp_kses_post($content['content']);
        
        ai_auto_blog_log("INFO: Preparing post data - Title: " . $title);
        
        $post_data = [
            'title' => sanitize_text_field($title),
            'content' => $cleaned_content,
            'status' => 'publish',
            'categories' => [intval($settings['category'])],
            'meta' => [
                'ai_generated' => true,
                'ai_prompt' => $settings['prompt']
            ]
        ];

        $api_url = trailingslashit($settings['site_url']) . 'wp-json/wp/v2/posts';
        ai_auto_blog_log("INFO: WordPress API URL: " . $api_url);

        $auth = base64_encode($settings['username'] . ':' . $settings['password']);
        
        $response = wp_remote_post($api_url, [
            'headers' => [
                'Authorization' => "Basic " . $auth,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json'
            ],
            'body' => json_encode($post_data),
            'timeout' => 60,
            'sslverify' => true,
            'data_format' => 'body'
        ]);

        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            ai_auto_blog_log("ERROR: WordPress API error - " . $error_message);
            throw new Exception(__('WordPress API error: ', 'ai-auto-blog') . $error_message);
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        
        ai_auto_blog_log("INFO: WordPress API Response Code: " . $response_code);
        ai_auto_blog_log("INFO: WordPress API Response Body: " . substr($response_body, 0, 500));

        if ($response_code !== 201) {
            $error_message = sprintf(
                __('Failed to create post. Status code: %d, Response: %s', 'ai-auto-blog'),
                $response_code,
                $response_body
            );
            ai_auto_blog_log("ERROR: " . $error_message);
            throw new Exception($error_message);
        }

        $response_data = json_decode($response_body, true);
        
        if (!isset($response_data['id'])) {
            ai_auto_blog_log("ERROR: No post ID in response - " . print_r($response_data, true));
            throw new Exception(__('Failed to get post ID from response', 'ai-auto-blog'));
        }

        ai_auto_blog_log("SUCCESS: Post created with ID: " . $response_data['id']);
        return $response_data['id'];
    }

    private static function attach_featured_image($post_id, $image_url) {
        try {
            if (empty($image_url)) {
                ai_auto_blog_log("INFO: No image URL provided for featured image");
                return;
            }

            ai_auto_blog_log("INFO: Attempting to attach featured image from URL: " . $image_url);
            
            $image_id = media_sideload_image($image_url, $post_id, __('AI Generated Featured Image', 'ai-auto-blog'), 'id');
            
            if (is_wp_error($image_id)) {
                throw new Exception($image_id->get_error_message());
            }

            $result = set_post_thumbnail($post_id, $image_id);
            
            if ($result) {
                ai_auto_blog_log("SUCCESS: Featured image attached - ID {$image_id}");
            } else {
                throw new Exception("Failed to set post thumbnail");
            }

        } catch (Exception $e) {
            ai_auto_blog_log("WARNING: Failed to attach featured image - " . $e->getMessage());
        }
    }
}

function ai_auto_blog_fetch_and_post() {
    return AI_Auto_Blog_Poster::fetch_and_post();
}

