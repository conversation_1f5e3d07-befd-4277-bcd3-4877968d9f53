<?php
if (!defined('ABSPATH')) exit;

class AI_Auto_Blog_Scheduler {
    const CRON_HOOK = 'ai_auto_blog_daily_post';
    const LOCK_TRANSIENT = 'ai_auto_blog_lock';
    const MAINTENANCE_HOOK = 'ai_auto_blog_maintenance';

    public static function init() {
        add_filter('cron_schedules', [__CLASS__, 'add_cron_schedules']);
        add_action(self::CRON_HOOK, [__CLASS__, 'execute_scheduled_post']);
        add_action(self::MAINTENANCE_HOOK, [__CLASS__, 'perform_maintenance']);
        
        register_activation_hook(__FILE__, [__CLASS__, 'activate']);
        register_deactivation_hook(__FILE__, [__CLASS__, 'deactivate']);
    }

    public static function add_cron_schedules($schedules) {
        $schedules['weekly'] = [
            'interval' => WEEK_IN_SECONDS,
            'display'  => __('Once Weekly', 'ai-auto-blog')
        ];
        $schedules['ai_auto_blog_custom'] = [
            'interval' => 15 * MINUTE_IN_SECONDS,
            'display'  => __('AI Auto Blog Custom', 'ai-auto-blog')
        ];
        return $schedules;
    }

    public static function activate() {
        self::schedule_events();
        wp_schedule_event(time(), 'ai_auto_blog_custom', self::MAINTENANCE_HOOK);
    }

    public static function deactivate() {
        wp_clear_scheduled_hook(self::CRON_HOOK);
        wp_clear_scheduled_hook(self::MAINTENANCE_HOOK);
        delete_transient(self::LOCK_TRANSIENT);
    }

    public static function schedule_events() {
        $settings = self::get_schedule_settings();
        $next_run = self::calculate_next_run($settings);

        wp_clear_scheduled_hook(self::CRON_HOOK);
        if ($settings['enabled']) {
            wp_schedule_event($next_run, $settings['frequency'], self::CRON_HOOK);
        }

        self::log_schedule_status($next_run, $settings);
    }

    public static function execute_scheduled_post() {
        if (get_transient(self::LOCK_TRANSIENT)) {
            ai_auto_blog_log('WARNING: Previous execution still running. Skipping this run.');
            return;
        }

        set_transient(self::LOCK_TRANSIENT, true, 10 * MINUTE_IN_SECONDS);

        try {
            require_once plugin_dir_path(__FILE__) . 'post-handler.php';
            $result = ai_auto_blog_fetch_and_post();
            
            if (is_wp_error($result)) {
                throw new Exception($result->get_error_message());
            }
            
            ai_auto_blog_log('SUCCESS: Scheduled post completed');
        } catch (Exception $e) {
            ai_auto_blog_log('ERROR: ' . $e->getMessage());
        } finally {
            delete_transient(self::LOCK_TRANSIENT);
        }
    }

    public static function perform_maintenance() {
        if (!wp_next_scheduled(self::CRON_HOOK)) {
            ai_auto_blog_log('WARNING: Main cron event missing, rescheduling');
            self::schedule_events();
        }
    }

    private static function get_schedule_settings() {
        return [
            'time' => get_option('ai_auto_blog_schedule_time', '09:00'),
            'frequency' => get_option('ai_auto_blog_schedule_frequency', 'daily'),
            'enabled' => get_option('ai_auto_blog_automation_enabled', 1)
        ];
    }

    private static function calculate_next_run($settings) {
        $current_time = current_time('timestamp');
        
        // Parse the time setting correctly
        list($hours, $minutes) = explode(':', $settings['time']);
        
        // Create a timestamp for today with the specified time
        $next_run = strtotime(date('Y-m-d', $current_time) . ' ' . $hours . ':' . $minutes . ':00');
        
        // If this time has already passed today, schedule for tomorrow
        if ($next_run <= $current_time) {
            $next_run += DAY_IN_SECONDS;
        }
        
        ai_auto_blog_log('DEBUG: Next run calculated as ' . date('Y-m-d H:i:s', $next_run) . ' from settings time ' . $settings['time']);
        
        return $next_run;
    }

    private static function log_schedule_status($next_run, $settings) {
        $status = [
            'Next Run' => date_i18n('Y-m-d H:i:s', $next_run),
            'Frequency' => $settings['frequency'],
            'Enabled' => $settings['enabled'] ? 'Yes' : 'No',
            'Time Setting' => $settings['time']
        ];

        ai_auto_blog_log('SCHEDULER: ' . json_encode($status));
    }

    public static function handle_settings_update($option) {
        if (in_array($option, ['ai_auto_blog_schedule_time', 'ai_auto_blog_schedule_frequency', 'ai_auto_blog_automation_enabled'])) {
            // Clear any existing lock to prevent conflicts
            delete_transient(self::LOCK_TRANSIENT);
            // Force immediate schedule update
            self::schedule_events();
            // Log the update
            ai_auto_blog_log('SCHEDULER: Settings updated, rescheduled events for option: ' . $option);
        }
    }
    
    // Manually reschedule events - can be called from outside
    public static function force_reschedule() {
        delete_transient(self::LOCK_TRANSIENT);
        self::schedule_events();
        return wp_next_scheduled(self::CRON_HOOK);
    }
}

// Initialize scheduler
AI_Auto_Blog_Scheduler::init();

// Handle settings updates
add_action('update_option', ['AI_Auto_Blog_Scheduler', 'handle_settings_update'], 10, 1);
add_action('add_option', ['AI_Auto_Blog_Scheduler', 'handle_settings_update'], 10, 1);